import { ThemeProvider } from "@/components/theme-provider";
import localFont from "next/font/local";
import "./globals.css";
import BgPattern from "@/components/bg-pattern";
import { Analytics } from "@vercel/analytics/react";
import { generateMetadata } from "@/lib/seo/generateMetadata";
import { LoadingProvider } from "@/context/loadingContext";
import ClientLayout from "@/components/clientLayout";
export const metadata = generateMetadata({});

const myFont = localFont({
  src: "./fonts/NotoSans.ttf",
  display: "swap",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <body className={myFont.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <div className="max-w-full transition-colors duration-300 overflow-hidden min-h-screen pt-6">
            <LoadingProvider>
              <ClientLayout>
                <BgPattern />
                {children}
              </ClientLayout>
            </LoadingProvider>
          </div>
        </ThemeProvider>
        <Analytics />
      </body>
    </html>
  );
}
