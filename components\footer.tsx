import {
  FaFacebook,
  FaLinkedin,
  FaPinterest,
  FaGithub,
  FaBehanceSquare,
} from "react-icons/fa";

export function Footer() {
  return (
    <footer className="sticky bottom-0 left-0 right-0 z-50 py-5 mt-16 bg-secondary rounded-t-2xl md:mt-20">
      <div className="container px-4 mx-auto sm:px-6 lg:px-4">
        <div className="flex justify-center space-x-6">
          <a
            href="https://github.com/Gedeon-luzolo"
            target="_blanc"
            className="text-gray-400 transition-colors hover:text-primary"
          >
            <FaGithub className="w-6 h-6" />
          </a>
          <a
            href="https://www.linkedin.com/in/gedeon-luzolo?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"
            target="_blanc"
            className="text-gray-400 transition-colors hover:text-primary"
          >
            <FaLinkedin className="w-6 h-6" />
          </a>
          <a
            href="https://www.linkedin.com/in/gedeon-luzolo?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"
            target="_blanc"
            className="text-gray-400 transition-colors hover:text-primary"
          >
            <FaBehanceSquare className="w-6 h-6" />
          </a>
          <a
            href="https://www.behance.net/gedeonluzolo"
            target="_blanc"
            className="text-gray-400 transition-colors hover:text-primary"
          >
            <FaFacebook className="w-6 h-6" />
          </a>
          <a
            href="https://pin.it/6a0sMMRBi"
            target="_blanc"
            className="text-gray-400 transition-colors hover:text-primary"
          >
            <FaPinterest className="w-6 h-6" />
          </a>
        </div>
        <p className="mt-4 text-sm text-center text-gray-600 dark:text-gray-400">
          © {new Date().getFullYear()} Gedeon Luzolo. All rights reserved.
        </p>
      </div>
    </footer>
  );
}
