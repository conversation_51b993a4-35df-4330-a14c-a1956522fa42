"use client";

import { IconArrowLeft, IconArrowRight } from "@tabler/icons-react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utilities/utils";

type Testimonial = {
  quote: string;
  name: string;
  designation: string;
  src: string;
};

export const AnimatedTestimonials = ({
  testimonials,
  autoplay = false,
  className,
}: {
  testimonials: Testimonial[];
  autoplay?: boolean;
  className?: string;
}) => {
  const [active, setActive] = useState(0);

  const handleNext = () => {
    setActive((prev) => (prev + 1) % testimonials.length);
  };

  const handlePrev = () => {
    setActive((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const isActive = (index: number) => {
    return index === active;
  };

  useEffect(() => {
    if (autoplay) {
      const interval = setInterval(handleNext, 5000);
      return () => clearInterval(interval);
    }
  }, [autoplay]);

  const randomRotateY = () => {
    return Math.floor(Math.random() * 21) - 10;
  };

  return (
    <div
      className={cn(
        "max-w-sm md:max-w-6xl mx-auto px-4 md:px-8 lg:px-12 py-12 md:py-20",
        className
      )}
    >
      <div className="relative grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-20">
        {/* Section des avatars */}
        <div className="order-2 md:order-1">
          <div className="relative h-64 md:h-80 w-full">
            <AnimatePresence mode="wait">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.src}
                  initial={{
                    opacity: 0,
                    scale: 0.8,
                    rotateY: randomRotateY(),
                  }}
                  animate={{
                    opacity: isActive(index) ? 1 : 0.6,
                    scale: isActive(index) ? 1 : 0.85,
                    rotateY: isActive(index) ? 0 : randomRotateY(),
                    zIndex: isActive(index) ? 20 : 10 - index,
                    y: isActive(index) ? [0, -20, 0] : 0,
                  }}
                  exit={{
                    opacity: 0,
                    scale: 0.8,
                    rotateY: randomRotateY(),
                  }}
                  transition={{
                    duration: 0.6,
                    ease: "easeInOut",
                  }}
                  className="absolute inset-0"
                  style={{ zIndex: isActive(index) ? 20 : 10 - index }}
                >
                  {/* Container avec glassmorphism harmonisé */}
                  <div className="relative h-full w-full rounded-3xl overflow-hidden backdrop-blur-md bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 shadow-xl ring-1 ring-slate-200/30 dark:ring-transparent">
                    {/* Border animé pour l'avatar actif */}
                    {isActive(index) && (
                      <motion.div
                        className="absolute inset-0 rounded-3xl bg-gradient-to-r from-orange-500/20 via-yellow-500/20 to-orange-600/20 opacity-70"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 0.7 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                      />
                    )}

                    <Image
                      src={testimonial.src}
                      alt={testimonial.name}
                      width={500}
                      height={500}
                      draggable={false}
                      className="h-full w-full object-cover object-center relative z-10"
                    />
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>

        {/* Section du contenu */}
        <div className="order-1 md:order-2 flex justify-between flex-col py-4 md:py-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={active}
              initial={{
                y: 30,
                opacity: 0,
              }}
              animate={{
                y: 0,
                opacity: 1,
              }}
              exit={{
                y: -30,
                opacity: 0,
              }}
              transition={{
                duration: 0.4,
                ease: "easeInOut",
              }}
              className="space-y-4 md:space-y-6"
            >
              {/* Badge de statut */}
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-orange-500/10 to-yellow-500/10 border border-white/20 dark:border-white/10 backdrop-blur-sm">
                <span className="text-xs font-medium text-orange-600 dark:text-orange-400">
                  Témoignage {active + 1} sur {testimonials.length}
                </span>
              </div>

              {/* Nom et titre */}
              <div className="space-y-2">
                <h3 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-200 bg-clip-text text-transparent">
                  {testimonials[active].name}
                </h3>
                <p className="text-sm md:text-base text-slate-600 dark:text-slate-400 font-medium">
                  {testimonials[active].designation}
                </p>
              </div>

              {/* Citation avec animation des mots */}
              <motion.div className="relative">
                <div className="absolute -top-2 -left-2 text-3xl md:text-4xl text-orange-500/20 font-serif"></div>
                <blockquote className="text-base md:text-lg text-slate-700 dark:text-slate-300 leading-relaxed pl-4 relative z-10">
                  {testimonials[active].quote.split(" ").map((word, index) => (
                    <motion.span
                      key={index}
                      initial={{
                        filter: "blur(10px)",
                        opacity: 0,
                        y: 10,
                      }}
                      animate={{
                        filter: "blur(0px)",
                        opacity: 1,
                        y: 0,
                      }}
                      transition={{
                        duration: 0.3,
                        ease: "easeInOut",
                        delay: 0.03 * index,
                      }}
                      className="inline-block"
                    >
                      {word}&nbsp;
                    </motion.span>
                  ))}
                </blockquote>
                <div className="absolute -bottom-2 -right-2 text-3xl md:text-4xl text-orange-500/20 font-serif rotate-180"></div>
              </motion.div>
            </motion.div>
          </AnimatePresence>

          {/* Contrôles de navigation */}
          <div className="flex items-center justify-between mt-8 md:mt-12">
            <div className="flex gap-3">
              <motion.button
                onClick={handlePrev}
                className="h-10 w-10 md:h-12 md:w-12 rounded-xl bg-gradient-to-r from-orange-500/10 to-yellow-500/10 backdrop-blur-sm border border-white/20 dark:border-white/10 flex items-center justify-center group transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/25"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <IconArrowLeft className="h-5 w-5 text-slate-700 dark:text-slate-300 group-hover:text-orange-600 dark:group-hover:text-orange-400 group-hover:-translate-x-0.5 transition-all duration-300" />
              </motion.button>

              <motion.button
                onClick={handleNext}
                className="h-10 w-10 md:h-12 md:w-12 rounded-xl bg-gradient-to-r from-orange-500/10 to-yellow-500/10 backdrop-blur-sm border border-white/20 dark:border-white/10 flex items-center justify-center group transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/25"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <IconArrowRight className="h-5 w-5 text-slate-700 dark:text-slate-300 group-hover:text-orange-600 dark:group-hover:text-orange-400 group-hover:translate-x-0.5 transition-all duration-300" />
              </motion.button>
            </div>

            {/* Indicateurs de progression */}
            <div className="flex gap-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActive(index)}
                  className={`h-2 w-8 rounded-full transition-all duration-300 ${
                    index === active
                      ? "bg-gradient-to-r from-orange-500 to-yellow-500"
                      : "bg-slate-300 dark:bg-slate-600 hover:bg-slate-400 dark:hover:bg-slate-500"
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
