"use client";

import { motion } from "framer-motion";

interface TechStackShowcaseProps {
  stack: string[];
  title: string;
}

export function TechStackShowcase({ stack, title }: TechStackShowcaseProps) {
  const getTechColor = (tech: string) => {
    const techColors: { [key: string]: string } = {
      // Frontend
      "React": "bg-blue-500/20 text-blue-700 dark:text-blue-300 border-blue-500/30",
      "Next.js": "bg-gray-800/20 text-gray-800 dark:text-gray-200 border-gray-800/30",
      "TypeScript": "bg-blue-600/20 text-blue-800 dark:text-blue-300 border-blue-600/30",
      "JavaScript": "bg-yellow-500/20 text-yellow-700 dark:text-yellow-300 border-yellow-500/30",
      "HTML5": "bg-orange-500/20 text-orange-700 dark:text-orange-300 border-orange-500/30",
      "CSS3": "bg-blue-400/20 text-blue-600 dark:text-blue-300 border-blue-400/30",
      "Tailwind CSS": "bg-cyan-500/20 text-cyan-700 dark:text-cyan-300 border-cyan-500/30",
      "Bootstrap": "bg-purple-500/20 text-purple-700 dark:text-purple-300 border-purple-500/30",
      "Framer Motion": "bg-pink-500/20 text-pink-700 dark:text-pink-300 border-pink-500/30",
      
      // Backend
      "Node.js": "bg-green-500/20 text-green-700 dark:text-green-300 border-green-500/30",
      "Express": "bg-gray-600/20 text-gray-700 dark:text-gray-300 border-gray-600/30",
      "Nest.js": "bg-red-500/20 text-red-700 dark:text-red-300 border-red-500/30",
      "PHP": "bg-indigo-500/20 text-indigo-700 dark:text-indigo-300 border-indigo-500/30",
      
      // Database
      "MongoDB": "bg-green-600/20 text-green-800 dark:text-green-300 border-green-600/30",
      "Prisma": "bg-slate-600/20 text-slate-700 dark:text-slate-300 border-slate-600/30",
      "Firebase": "bg-orange-400/20 text-orange-600 dark:text-orange-300 border-orange-400/30",
      "Socket.io": "bg-gray-700/20 text-gray-800 dark:text-gray-300 border-gray-700/30",
      
      // Design Tools
      "Figma": "bg-purple-400/20 text-purple-600 dark:text-purple-300 border-purple-400/30",
      "Adobe XD": "bg-pink-600/20 text-pink-700 dark:text-pink-300 border-pink-600/30",
      "Photoshop": "bg-blue-700/20 text-blue-800 dark:text-blue-300 border-blue-700/30",
      "Illustrator": "bg-orange-600/20 text-orange-700 dark:text-orange-300 border-orange-600/30",
      "InDesign": "bg-pink-700/20 text-pink-800 dark:text-pink-300 border-pink-700/30",
      "After Effects": "bg-purple-600/20 text-purple-700 dark:text-purple-300 border-purple-600/30",
      "Sketch": "bg-yellow-600/20 text-yellow-700 dark:text-yellow-300 border-yellow-600/30",
      "Principle": "bg-blue-500/20 text-blue-600 dark:text-blue-300 border-blue-500/30",
    };
    
    return techColors[tech] || "bg-orange-500/20 text-orange-700 dark:text-orange-300 border-orange-500/30";
  };

  return (
    <motion.div
      className="p-6 rounded-2xl bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <motion.h3
        className="text-lg font-semibold mb-4 text-slate-800 dark:text-white"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.4, delay: 0.2 }}
      >
        {title}
      </motion.h3>
      
      <div className="flex flex-wrap gap-3">
        {stack.map((tech, index) => (
          <motion.span
            key={tech}
            className={`px-3 py-2 rounded-xl text-sm font-medium border backdrop-blur-sm ${getTechColor(tech)}`}
            initial={{ scale: 0, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ 
              duration: 0.4, 
              delay: index * 0.1,
              type: "spring",
              bounce: 0.4
            }}
            whileHover={{ 
              scale: 1.05, 
              y: -2,
              transition: { duration: 0.2 }
            }}
          >
            {tech}
          </motion.span>
        ))}
      </div>
    </motion.div>
  );
}

// Composant pour afficher toutes les stacks de technologies
export function AllTechStacks() {
  const techStacks = [
    {
      title: "Frontend Development",
      stack: ["React", "Next.js", "TypeScript", "Tailwind CSS", "Framer Motion", "HTML5", "CSS3", "JavaScript"]
    },
    {
      title: "Backend Development", 
      stack: ["Node.js", "Express", "Nest.js", "PHP", "Prisma", "MongoDB", "Firebase", "Socket.io"]
    },
    {
      title: "Design & Prototyping",
      stack: ["Figma", "Adobe XD", "Photoshop", "Illustrator", "InDesign", "After Effects", "Sketch", "Principle"]
    }
  ];

  return (
    <section className="py-16 px-6">
      <div className="max-w-6xl mx-auto">
        <motion.h2
          className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-slate-900 via-orange-900 to-orange-800 dark:from-white dark:via-orange-100 dark:to-orange-200 bg-clip-text text-transparent"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Technologies & Outils
        </motion.h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {techStacks.map((category, index) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <TechStackShowcase 
                title={category.title}
                stack={category.stack}
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
