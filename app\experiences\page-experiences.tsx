"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Check } from "lucide-react";
import { useInView } from "framer-motion";
import { useLoading } from "@/context/loadingContext";
import { useEffect } from "react";
import { experiences } from "@/lib/utilities/accessoire";

export default function PageExperiences() {
  const ref = React.useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const { setIsLoading } = useLoading();

  useEffect(() => {
    setIsLoading(false);
  }, [setIsLoading]);

  return (
    <section className="relative min-h-screen py-24 sm:py-36 z-100 max-w-[90%] sm:max-w-full mx-auto">
      <div className="container mx-auto px-4">
        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 2 }}
          className="mb-8 text-3xl sm:text-4xl md:text-5xl font-bold"
        >
          Expériences
        </motion.h1>

        <div ref={ref} className="relative space-y-12 sm:space-y-20">
          {/* Timeline line */}
          <div className="absolute left-[18px] sm:left-[27px] top-[36px] sm:top-[52px] h-[calc(100%-60px)] sm:h-[calc(100%-84px)] w-[2px] bg-zinc-800" />

          {experiences.map((experience, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={isInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 1, delay: index * 1 }}
              className="relative flex gap-4 sm:gap-8"
            >
              {/* Checkmark circle */}
              <div className="relative z-10 flex h-10 w-10 sm:h-14 sm:w-14 shrink-0 items-center justify-center rounded-full border-2 border-zinc-800 bg-zinc-900 ">
                <Check className="h-4 w-4 sm:h-6 sm:w-6 text-primary" />
              </div>

              <div className="pt-1 sm:pt-2">
                <h2 className="text-lg sm:text-xl md:text-2xl font-semibold">
                  {experience.date}
                </h2>
                <h3 className="mt-1 sm:mt-2 text-xl sm:text-2xl md:text-3xl font-bold">
                  {experience.company}
                </h3>
                <p className="mt-2 sm:mt-4 max-w-2xl text-base sm:text-lg leading-relaxed text-zinc-400">
                  {experience.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
