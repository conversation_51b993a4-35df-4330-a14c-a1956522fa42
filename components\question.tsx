import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { ChevronRight } from "lucide-react";
import { motion } from "motion/react";

export function Question() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-[80%] md:max-w-[70%] mx-auto grid items-center grid-cols-1 justify-between lg:grid-cols-2 py-16 "
    >
      <div>
        <h2 className="mb-12 text-3xl font-bold text-center text-gray-800 md:text-5xl md:text-left dark:text-white">
          Les questions fréquentes
        </h2>
      </div>
      <Accordion
        className="flex w-full flex-col"
        transition={{ type: "spring", stiffness: 120, damping: 20 }}
        variants={{
          expanded: {
            opacity: 1,
            scale: 1,
          },
          collapsed: {
            opacity: 0,
            scale: 0.7,
          },
        }}
      >
        <AccordionItem value="animation-properties" className="py-2">
          <AccordionTrigger className="w-full py-0.5 text-left text-zinc-950 dark:text-zinc-50">
            <div className="flex items-center">
              <ChevronRight className="h-4 w-4 text-zinc-950 transition-transform duration-200 group-data-expanded:rotate-90 dark:text-zinc-50" />
              <div className="ml-2 text-zinc-950 dark:text-zinc-50">
                Quels langages et technologies utilisez-vous ?
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="origin-left">
            <p className="pl-6 pr-2 py-2 text-sm text-zinc-500 dark:text-zinc-400">
              J’utilise des technologies modernes comme HTML, CSS, JavaScript
              (ES6+), Typescript, React.js, Next js, Node.js, Express.js, Nest
              js, MongoDB, MySQL, Prisma et Firebase.
            </p>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="animation" className="py-2">
          <AccordionTrigger className="w-full py-0.5 text-left text-zinc-950 dark:text-zinc-50">
            <div className="flex items-center">
              <ChevronRight className="h-4 w-4 text-zinc-950 transition-transform duration-200 group-data-expanded:rotate-90 dark:text-zinc-50" />
              <div className="ml-2 text-zinc-950 dark:text-zinc-50">
                Quels sont vos délais moyens pour un projet ?
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="origin-left">
            <p className="pl-6 pr-2 py-2 text-sm text-zinc-500 dark:text-zinc-400">
              Cela dépend du projet, mais voici une estimation : <br /> Site
              vitrine : 4 jours à 1 semaines <br /> Site e-commerce : 3 à 6
              semaines <br />
              Branding complet : 1 semaines <br /> UI/UX Design : 1 à 3 semaines
            </p>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="advanced-features" className="py-2">
          <AccordionTrigger className="w-full py-0.5 text-left text-zinc-950 dark:text-zinc-50">
            <div className="flex items-center">
              <ChevronRight className="h-4 w-4 text-zinc-950 transition-transform duration-200 group-data-expanded:rotate-90 dark:text-zinc-50" />
              <div className="ml-2 text-zinc-950 dark:text-zinc-50">
                Quelles sont vos spécialités en design graphique ?
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="origin-left">
            <p className="pl-6 pr-2 py-2 text-sm text-zinc-500 dark:text-zinc-400">
              Je me spécialise dans la création d’identités visuelles, le
              branding, la conception de supports marketing (affiches, cartes de
              visite, brochures, etc.) et le design de contenu digital.
            </p>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="advanced" className="py-2">
          <AccordionTrigger className="w-full py-0.5 text-left text-zinc-950 dark:text-zinc-50">
            <div className="flex items-center">
              <ChevronRight className="h-4 w-4 text-zinc-950 transition-transform duration-200 group-data-expanded:rotate-90 dark:text-zinc-50" />
              <div className="ml-2 text-zinc-950 dark:text-zinc-50">
                Quels sont vos tarifs ou comment établissez-vous un devis ?
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="origin-left">
            <p className="pl-6 pr-2  py-2 text-sm text-zinc-500 dark:text-zinc-400">
              Mes tarifs varient en fonction du projet. Je propose des devis
              personnalisés après une discussion pour comprendre vos besoins.
              Contactez-moi pour un devis gratuit !
            </p>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="community-support" className="py-2">
          <AccordionTrigger className="w-full py-0.5 text-left text-zinc-950 dark:text-zinc-50">
            <div className="flex items-center">
              <ChevronRight className="h-4 w-4 text-zinc-950 transition-transform duration-200 group-data-expanded:rotate-90 dark:text-zinc-50" />
              <div className="ml-2 text-zinc-950 dark:text-zinc-50">
                Comment travaillez-vous avec vos clients pour concevoir un logo
                efficace et mémorable ?
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="origin-left">
            <p className="pl-6 pr-2 py-2 text-sm text-zinc-500 dark:text-zinc-400">
              Je commence par une discussion approfondie pour comprendre votre
              vision. Ensuite, je propose plusieurs concepts et affine le design
              en fonction de vos retours jusqu’à obtenir un logo unique et
              puissant.
            </p>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </motion.div>
  );
}
