{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.1", "@tabler/icons-react": "^3.33.0", "@vercel/analytics": "^1.4.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.1", "framer-motion": "^12.15.0", "lucide-react": "^0.460.0", "motion": "^11.11.17", "next": "^15.0.3", "next-themes": "^0.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/dotenv": "^6.1.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}