export const getTechColor = (tech: string): string => {
  const techColors: Record<string, string> = {
    // Frontend
    React: "bg-blue-500/20 text-blue-700 dark:text-blue-300 border-blue-500/30",
    "Next.js":
      "bg-gray-800/20 text-gray-800 dark:text-gray-200 border-gray-800/30",
    TypeScript:
      "bg-blue-600/20 text-blue-800 dark:text-blue-300 border-blue-600/30",
    JavaScript:
      "bg-yellow-500/20 text-yellow-700 dark:text-yellow-300 border-yellow-500/30",
    HTML5:
      "bg-orange-500/20 text-orange-700 dark:text-orange-300 border-orange-500/30",
    CSS3: "bg-blue-400/20 text-blue-600 dark:text-blue-300 border-blue-400/30",
    "Tailwind CSS":
      "bg-cyan-500/20 text-cyan-700 dark:text-cyan-300 border-cyan-500/30",
    Bootstrap:
      "bg-purple-500/20 text-purple-700 dark:text-purple-300 border-purple-500/30",
    "Framer Motion":
      "bg-pink-500/20 text-pink-700 dark:text-pink-300 border-pink-500/30",

    // Backend
    "Node.js":
      "bg-green-500/20 text-green-700 dark:text-green-300 border-green-500/30",
    Express:
      "bg-gray-600/20 text-gray-700 dark:text-gray-300 border-gray-600/30",
    "Nest.js": "bg-red-500/20 text-red-700 dark:text-red-300 border-red-500/30",
    PHP: "bg-indigo-500/20 text-indigo-700 dark:text-indigo-300 border-indigo-500/30",

    // Database
    MongoDB:
      "bg-green-600/20 text-green-800 dark:text-green-300 border-green-600/30",
    Prisma:
      "bg-slate-600/20 text-slate-700 dark:text-slate-300 border-slate-600/30",
    Firebase:
      "bg-orange-400/20 text-orange-600 dark:text-orange-300 border-orange-400/30",
    "Socket.io":
      "bg-gray-700/20 text-gray-800 dark:text-gray-300 border-gray-700/30",

    // Design Tools
    Figma:
      "bg-purple-400/20 text-purple-600 dark:text-purple-300 border-purple-400/30",
    "Adobe XD":
      "bg-pink-600/20 text-pink-700 dark:text-pink-300 border-pink-600/30",
    Photoshop:
      "bg-blue-700/20 text-blue-800 dark:text-blue-300 border-blue-700/30",
    Illustrator:
      "bg-orange-600/20 text-orange-700 dark:text-orange-300 border-orange-600/30",
    InDesign:
      "bg-pink-700/20 text-pink-800 dark:text-pink-300 border-pink-700/30",
    "After Effects":
      "bg-purple-600/20 text-purple-700 dark:text-purple-300 border-purple-600/30",
    Sketch:
      "bg-yellow-600/20 text-yellow-700 dark:text-yellow-300 border-yellow-600/30",
    Principle:
      "bg-blue-500/20 text-blue-600 dark:text-blue-300 border-blue-500/30",
  };

  return (
    techColors[tech] ||
    "bg-orange-500/20 text-orange-700 dark:text-orange-300 border-orange-500/30"
  );
};
