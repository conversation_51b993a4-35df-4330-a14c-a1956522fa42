"use client";

import { useLoading } from "@/context/loadingContext";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isLoading } = useLoading();

  return (
    <>
      {!isLoading && <Navbar />}
      {children}
      {!isLoading && <Footer />}
    </>
  );
}
