"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useMotionValue, useSpring, useTransform } from "framer-motion";

export interface MousePosition {
  x: number;
  y: number;
}

export interface ParallaxConfig {
  strength?: number;
  enableRotation?: boolean;
  enableScale?: boolean;
  dampening?: number;
}

// Hook for advanced mouse tracking and parallax effects
export function useAdvancedMouseEffects(config: ParallaxConfig = {}) {
  const {
    strength = 0.1,
    enableRotation = true,
    enableScale = false,
    dampening = 0.1,
  } = config;

  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  // Motion values for smooth animations
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  // Spring animations for smooth movement
  const springConfig = { stiffness: 300, damping: 30 };
  const rotateX = useSpring(useTransform(mouseY, [-300, 300], [15, -15]), springConfig);
  const rotateY = useSpring(useTransform(mouseX, [-300, 300], [-15, 15]), springConfig);
  const scale = useSpring(1, springConfig);

  const handleMouseMove = useCallback((e: MouseEvent | React.MouseEvent) => {
    if (!elementRef.current) return;

    const rect = elementRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const x = e.clientX - centerX;
    const y = e.clientY - centerY;

    setMousePosition({ x, y });
    
    if (enableRotation) {
      mouseX.set(x * strength);
      mouseY.set(y * strength);
    }
  }, [enableRotation, strength, mouseX, mouseY]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
    if (enableScale) {
      scale.set(1.05);
    }
  }, [enableScale, scale]);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    mouseX.set(0);
    mouseY.set(0);
    if (enableScale) {
      scale.set(1);
    }
  }, [mouseX, mouseY, enableScale, scale]);

  return {
    elementRef,
    mousePosition,
    isHovered,
    rotateX,
    rotateY,
    scale,
    handleMouseMove,
    handleMouseEnter,
    handleMouseLeave,
  };
}

// Hook for scroll-based parallax effects
export function useScrollParallax(speed: number = 0.5) {
  const [scrollY, setScrollY] = useState(0);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (elementRef.current) {
        const rect = elementRef.current.getBoundingClientRect();
        const scrolled = window.pageYOffset;
        const rate = scrolled * speed;
        setScrollY(rate);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [speed]);

  return { elementRef, scrollY };
}

// Hook for intersection observer with advanced options
export function useAdvancedIntersection(options: IntersectionObserverInit = {}) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [intersectionRatio, setIntersectionRatio] = useState(0);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        setIntersectionRatio(entry.intersectionRatio);
      },
      {
        threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1],
        ...options,
      }
    );

    observer.observe(element);
    return () => observer.disconnect();
  }, [options]);

  return { elementRef, isIntersecting, intersectionRatio };
}

// Hook for magnetic button effects
export function useMagneticEffect(strength: number = 0.3) {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const elementRef = useRef<HTMLElement>(null);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!elementRef.current) return;

    const rect = elementRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = (e.clientX - centerX) * strength;
    const deltaY = (e.clientY - centerY) * strength;

    setPosition({ x: deltaX, y: deltaY });
  }, [strength]);

  const handleMouseLeave = useCallback(() => {
    setPosition({ x: 0, y: 0 });
  }, []);

  return {
    elementRef,
    position,
    handleMouseMove,
    handleMouseLeave,
  };
}

// Hook for ripple effects
export function useRippleEffect() {
  const [ripples, setRipples] = useState<Array<{ x: number; y: number; id: number }>>([]);
  const elementRef = useRef<HTMLElement>(null);

  const createRipple = useCallback((e: React.MouseEvent) => {
    if (!elementRef.current) return;

    const rect = elementRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const id = Date.now();

    setRipples(prev => [...prev, { x, y, id }]);

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== id));
    }, 600);
  }, []);

  return {
    elementRef,
    ripples,
    createRipple,
  };
}

// Hook for floating particles effect
export function useFloatingParticles(count: number = 20) {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    speed: number;
    opacity: number;
  }>>([]);

  useEffect(() => {
    const newParticles = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 1,
      speed: Math.random() * 2 + 0.5,
      opacity: Math.random() * 0.5 + 0.2,
    }));

    setParticles(newParticles);

    const interval = setInterval(() => {
      setParticles(prev => prev.map(particle => ({
        ...particle,
        y: particle.y <= -10 ? 110 : particle.y - particle.speed * 0.1,
        x: particle.x + Math.sin(Date.now() * 0.001 + particle.id) * 0.1,
      })));
    }, 50);

    return () => clearInterval(interval);
  }, [count]);

  return particles;
}

// Hook for advanced color transitions
export function useColorTransition(colors: string[], duration: number = 3000) {
  const [currentColorIndex, setCurrentColorIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentColorIndex(prev => (prev + 1) % colors.length);
        setIsTransitioning(false);
      }, duration / 2);
    }, duration);

    return () => clearInterval(interval);
  }, [colors.length, duration]);

  return {
    currentColor: colors[currentColorIndex],
    nextColor: colors[(currentColorIndex + 1) % colors.length],
    isTransitioning,
  };
}

// Hook for performance monitoring
export function usePerformanceMonitor() {
  const [fps, setFps] = useState(60);
  const [isLowPerformance, setIsLowPerformance] = useState(false);

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const currentFPS = Math.round((frameCount * 1000) / (currentTime - lastTime));
        setFps(currentFPS);
        setIsLowPerformance(currentFPS < 30);
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFPS);
    };

    const animationId = requestAnimationFrame(measureFPS);
    return () => cancelAnimationFrame(animationId);
  }, []);

  return { fps, isLowPerformance };
}
