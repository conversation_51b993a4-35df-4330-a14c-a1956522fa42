"use client";


export default function BgPattern() {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* Background gradient principal */}

      <div className="fixed top-0 right-0 left-0 inset-0 opacity-10 z-[-1]" />

      {/* Orbes additionnels pour plus de profondeur */}
      <div className="absolute w-64 h-64 rounded-full bg-gradient-to-r from-emerald-400/8 to-cyan-600/8 blur-2xl left-[60%] top-[10%]" />
      <div className="absolute w-72 h-72 rounded-full bg-gradient-to-r from-violet-400/8 to-fuchsia-600/8 blur-2xl left-[5%] bottom-[10%]" />

      {/* Pattern subtil en overlay */}
      <div
        className="absolute inset-0 opacity-[0.40]"
        style={{
          backgroundImage: `radial-gradient(circle at 2px 2px, rgba(249, 115, 22, 0.3) 1px, transparent 0)`,
          backgroundSize: "32px 32px",
        }}
      />
    </div>
  );
}
