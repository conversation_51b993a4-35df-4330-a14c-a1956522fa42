"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { FaEye, FaExternalLinkAlt, FaCode, FaPalette } from "react-icons/fa";
import { projects } from "@/lib/accessoire";
import { cn } from "@/lib/utils";
import { getTechColor } from "@/lib/utilities/function";

const categories = ["Tout", "Website", "Branding", "UX", "Design", "Logo"];

interface Project {
  id: number;
  title: string;
  description: string;
  image: string;
  category: string;
  href: string;
  target?: string;
  stack: string[];
}

export function AdvancedPortfolio() {
  const [activeCategory, setActiveCategory] = useState("Tout");
  const [hoveredProject, setHoveredProject] = useState<number | null>(null);

  const filteredProjects =
    activeCategory === "Tout"
      ? projects
      : projects.filter((project) => project.category === activeCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Website":
        return <FaCode className="w-4 h-4" />;
      case "Design":
        return <FaPalette className="w-4 h-4" />;
      case "Branding":
        return <FaPalette className="w-4 h-4" />;
      case "Logo":
        return <FaPalette className="w-4 h-4" />;
      default:
        return <FaEye className="w-4 h-4" />;
    }
  };

  return (
    <section className="relative min-h-screen py-20">
      <div className="relative z-20 max-w-7xl mx-auto px-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-6xl md:text-7xl font-bold mb-6 dark:text-slate-300 ">
            Portfolio
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
            Découvrez mes créations où l&apos;innovation rencontre
            l&apos;excellence
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
        >
          {categories.map((category, index) => (
            <motion.button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={cn(
                "relative px-8 py-4 rounded-2xl font-medium transition-all duration-300",
                "backdrop-blur-sm border border-white/20 dark:border-white/10",
                activeCategory === category
                  ? "bg-gradient-to-r from-orange-500/20 via-yellow-500/20 to-orange-600/20 text-orange-700 dark:text-orange-300 shadow-lg"
                  : "bg-white/10 dark:bg-white/5 text-slate-700 dark:text-slate-300 hover:bg-white/20 dark:hover:bg-white/10"
              )}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              viewport={{ once: true }}
            >
              <span className="flex items-center gap-2">
                {getCategoryIcon(category)}
                {category}
              </span>
              {activeCategory === category && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-orange-500/20 via-yellow-500/20 to-orange-600/20 rounded-2xl -z-10"
                  layoutId="activeCategory"
                  transition={{ type: "spring", bounce: 0.15, duration: 0.4 }}
                />
              )}
            </motion.button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          layout
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <AnimatePresence>
            {filteredProjects.map((project, index) => (
              <ProjectCard
                key={`${activeCategory}-${project.id}`}
                project={project}
                index={index}
                isHovered={hoveredProject === project.id}
                hasHoveredSibling={
                  hoveredProject !== null && hoveredProject !== project.id
                }
                onHover={() => setHoveredProject(project.id)}
                onLeave={() => setHoveredProject(null)}
              />
            ))}
          </AnimatePresence>
        </motion.div>
      </div>
    </section>
  );
}

interface ProjectCardProps {
  project: Project;
  index: number;
  isHovered: boolean;
  hasHoveredSibling: boolean;
  onHover: () => void;
  onLeave: () => void;
}

function ProjectCard({
  project,
  index,
  isHovered,
  hasHoveredSibling,
  onHover,
  onLeave,
}: ProjectCardProps) {
  return (
    <motion.div
      className="group relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{
        opacity: hasHoveredSibling ? 0.7 : 1,
        y: 0,
        scale: hasHoveredSibling ? 0.98 : 1,
      }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        duration: 0.4,
        delay: index * 0.05,
        ease: "easeOut",
      }}
      onMouseEnter={onHover}
      onMouseLeave={onLeave}
    >
      <motion.div
        className="relative h-[400px] rounded-3xl overflow-hidden"
        whileHover={{
          y: -8,
          scale: 1.02,
          transition: { duration: 0.3, ease: "easeOut" },
        }}
        animate={{
          y: isHovered ? -4 : 0,
        }}
      >
        {/* Carte avec glassmorphism simplifié */}
        <div
          className={`absolute inset-0 backdrop-blur-md border rounded-3xl shadow-xl ring-1 transition-all duration-300 ${
            hasHoveredSibling
              ? "bg-gradient-to-br from-white/30 via-white/20 to-white/15 dark:from-white/15 dark:via-white/8 dark:to-white/5 border-white/40 dark:border-white/15 ring-slate-200/60 dark:ring-transparent"
              : "bg-gradient-to-br from-white/20 via-white/10 to-white/5 dark:from-white/10 dark:via-white/5 dark:to-white/2 border-white/30 dark:border-white/10 ring-slate-200/50 dark:ring-transparent"
          }`}
        >
          {/* Border animé au hover uniquement */}
          {isHovered && (
            <motion.div
              className="absolute inset-0 rounded-3xl opacity-60"
              style={{
                background:
                  "linear-gradient(45deg, rgba(249, 115, 22, 0.25), rgba(234, 179, 8, 0.25))",
              }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.6 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            />
          )}

          {/* Contenu de la carte */}
          <div className="relative z-10 h-full p-1 rounded-3xl">
            <div
              className={`h-full backdrop-blur-sm rounded-3xl overflow-hidden transition-all duration-300 ${
                hasHoveredSibling
                  ? "bg-gradient-to-br from-white/50 via-white/30 to-white/20 dark:from-slate-900/40 dark:via-slate-800/25 dark:to-slate-900/15 border border-slate-200/50 dark:border-slate-700/30"
                  : "bg-gradient-to-br from-white/40 via-white/20 to-white/10 dark:from-slate-900/30 dark:via-slate-800/15 dark:to-slate-900/5 border border-slate-200/40 dark:border-transparent"
              }`}
            >
              {/* Image du projet */}
              <div className="relative h-48 overflow-hidden rounded-t-3xl">
                <motion.div
                  className="absolute inset-0"
                  whileHover={{
                    scale: 1.05,
                    transition: { duration: 0.4, ease: "easeOut" },
                  }}
                >
                  <Image
                    src={project.image}
                    alt={project.title}
                    fill
                    className="object-cover"
                  />
                </motion.div>

                {/* Overlay au hover */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />

                {/* Badge de catégorie */}
                <div className="absolute top-4 left-4 px-3 py-1 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 text-white text-sm font-medium">
                  {project.category}
                </div>
              </div>

              {/* Contenu */}
              <div className="p-6 h-[calc(100%-12rem)] flex flex-col justify-between">
                <div>
                  <h3
                    className={`text-xl font-bold mb-3 transition-colors duration-300 ${
                      hasHoveredSibling
                        ? "text-slate-900 dark:text-white"
                        : "text-slate-800 dark:text-white"
                    }`}
                  >
                    {project.title}
                  </h3>
                  <p
                    className={`text-sm leading-relaxed mb-4 transition-colors duration-300 ${
                      hasHoveredSibling
                        ? "text-slate-700 dark:text-slate-200"
                        : "text-slate-600 dark:text-slate-300"
                    }`}
                  >
                    {project.description}
                  </p>

                  {/* Tech Stack Badges */}
                  <div className="flex flex-wrap gap-1.5">
                    {project.stack.slice(0, 4).map((tech) => (
                      <span
                        key={tech}
                        className={`px-1.5 py-0.5 rounded-md text-[10px] font-medium border backdrop-blur-sm transition-all duration-300 ${getTechColor(
                          tech
                        )}`}
                      >
                        {tech}
                      </span>
                    ))}
                    {project.stack.length > 4 && (
                      <span className="px-1.5 py-0.5 rounded-md text-[10px] font-medium bg-gray-500/20 text-gray-600 dark:text-gray-400 border border-gray-500/30 backdrop-blur-sm">
                        +{project.stack.length - 4}
                      </span>
                    )}
                  </div>
                </div>

                {/* Bouton d'action */}
                <div className="mt-4">
                  <Link href={project.href} target={project.target}>
                    <motion.button
                      className={`group/btn w-full py-3 px-6 rounded-2xl backdrop-blur-sm border font-medium transition-all duration-300 ${
                        hasHoveredSibling
                          ? "bg-gradient-to-r from-orange-500/20 to-yellow-500/20 border-white/30 dark:border-white/15 text-slate-800 dark:text-white"
                          : "bg-gradient-to-r from-orange-500/15 to-yellow-500/15 border-white/20 dark:border-white/10 text-slate-700 dark:text-white"
                      }`}
                      whileHover={{
                        scale: 1.02,
                        backgroundColor: "rgba(249, 115, 22, 0.15)",
                        transition: { duration: 0.2 },
                      }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="flex items-center justify-center gap-2">
                        Voir le projet
                        <motion.div
                          whileHover={{ x: 2 }}
                          transition={{ duration: 0.2 }}
                        >
                          <FaExternalLinkAlt className="w-4 h-4" />
                        </motion.div>
                      </span>
                    </motion.button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Effet de glow simplifié harmonisé */}
        <motion.div
          className="absolute -inset-2 bg-gradient-to-r from-orange-500/10 via-yellow-500/10 to-orange-600/10 rounded-3xl blur-lg -z-10"
          initial={{ opacity: 0 }}
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        />
      </motion.div>
    </motion.div>
  );
}
