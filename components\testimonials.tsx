"use client";

import { motion } from "framer-motion";

import { testimonials } from "@/lib/accessoire";
import { AnimatedTestimonials } from "./ui/animated-testimonials";

export default function Testimonials() {
  return (
    <section className="pt-8 px-4">
      <div className="w-[80%] m-auto flex flex-col lg:py-20 gap-5 items-center">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 2 }}
          className="space-y-6 text-center"
        >
          <h2 className="text-4xl md:text-5xl font-bold tracking-tight">
            Ce qu’ils disent de moi
          </h2>
          <p className="text-lg text-muted-foreground max-w-4xl">
            Je suis honoré de collaborer avec des clients et partenaires
            exceptionnels. Voici quelques témoignages de ceux qui m’ont fait
            confiance pour concrétiser leurs idées et renforcer leur image de
            marque.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: -28 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 3 }}
          className="space-y-6 "
        >
          <AnimatedTestimonials testimonials={testimonials} />
        </motion.div>
      </div>
    </section>
  );
}
