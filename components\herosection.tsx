"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import profil1 from "../public/images/profil1.png";
import { ButtonContact } from "./ButtonContact";

export default function HeroSection() {
  return (
    <section className="relative overflow-hidden">
      <div className="flex-col-reverse w-[80%] mx-auto relative px-4 md:px-6 flex py-24 sm:flex-row md:items-center md:gap-8 lg:gap-16 lg:py-16 xl:py-24">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 2, delay: 0.5 }}
          className="flex flex-col gap-4 md:gap-6 lg:gap-8 md:w-1/2"
        >
          <div className="inline-flex items-center gap-2 rounded-lg bg-muted px-3 py-1 text-sm text-muted-foreground w-fit">
            <span className="font-medium">Solutions IT Excellence</span>
          </div>
          <h1 className="text-3xl font-bold sm:text-4xl md:text-4xl lg:text-5xl">
            Gedeon LUZOLO
          </h1>
          <p className="text-muted-foreground md:text-lg lg:text-xl">
            Je vous accompagne avec passion pour créer des expériences visuelles
            uniques, que ce soit pour votre site web ou d&apos;autres projets.
          </p>
          <div className="my-4 w-full flex justify-start">
            <ButtonContact />
          </div>
        </motion.div>

        {/* Image Grand ecran */}

        <motion.div
          initial={{ opacity: 0, scale: 0.1 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1.3, delay: 0.5 }}
          className="hidden relative md:w-1/2 sm:block"
        >
          <div className="relative h-[320px] md:h-[500px] lg:h-[500px]">
            <Image
              src={profil1}
              alt="Hero Image"
              fill
              className="object-contain"
              priority
            />
          </div>
        </motion.div>

        {/* Image petit ecran */}

        <motion.div
          initial={{ opacity: 0, scale: 0.1 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1.3, delay: 0.5 }}
          className="block relative right-24 sm:hidden mb-3"
        >
          <div className="relative h-[100px]">
            <Image
              src="/images/profil3.png"
              alt="Hero Image"
              fill
              className="object-contain"
              priority
            />
          </div>
        </motion.div>
      </div>
    </section>
  );
}
