"use client";
// import { Projet } from "@/components/projet";
import { Services } from "@/components/services";
import HeroSection from "@/components/herosection";
import { Question } from "@/components/question";
import { useEffect } from "react";
import { useLoading } from "@/context/loadingContext";
import { motion } from "framer-motion";
import { Loader } from "@/components/loader";
import { AdvancedPortfolio } from "@/components/advanced-portfolio";
import Testimonials from "@/components/testimonials";

export default function Home() {
  const { isLoading, setIsLoading } = useLoading();

  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 4000);
  }, [setIsLoading]);

  return (
    <div className="overflow-hidden">
      {isLoading && <Loader />}
      {!isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
        >
          <HeroSection />
          <Services />
         <AdvancedPortfolio />
          {/* <Projet /> */}
          <Testimonials />
          <Question />
        </motion.div>
      )}
    </div>
  );
}
