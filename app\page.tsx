"use client";

import HeroSection from "@/components/herosection";
import { Question } from "@/components/question";
import { useEffect } from "react";
import { useLoading } from "@/context/loadingContext";
import { motion } from "framer-motion";
import { Loader } from "@/components/loader";
import { AdvancedPortfolio } from "@/components/portfolio/advanced-portfolio";
import Testimonials from "@/components/testimonials";
import { Services } from "@/components/services";

export default function Home() {
  const { isLoading, setIsLoading } = useLoading();

  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 3000);
  }, [setIsLoading]);

  return (
    <div className="overflow-hidden">
      {isLoading && <Loader />}
      {!isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
        >
          <HeroSection />
          <Services />
          <AdvancedPortfolio />
          <Testimonials />
          <Question />
        </motion.div>
      )}
    </div>
  );
}
