import PageExperiences from "./page-experiences";
import { generateMetadata } from "@/lib/seo/generateMetadata";

export const metadata = generateMetadata({
  title: "Expériences de Gédéon Luzolo  | Graphiste & Développeur Front-End",
  description:
    "Explorez les expériences professionnelles de Gédéon Luzolo dans le graphisme, le branding et le développement front-end",
  Keywords: [
    "Expériences",
    "portfolio",
    "Gédéon Luzolo",
    "Branding",
    "Logotype",
    "Freelance",
    "Regideso",
    "design graphique",
    " UI/UX",
    "front-end",
  ],
  image: {
    url: "/images/Opengraph/opengraph_experiences.png",
    alt: "Photo des mes expériences profesionnels",
  },
});
export default function Page() {
  return (
    <div>
      <PageExperiences />
    </div>
  );
}
