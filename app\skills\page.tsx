import PageSkills from "./page-skills";
import { generateMetadata } from "@/lib/seo/generateMetadata";

export const metadata = generateMetadata({
  title: "Compétences de Gédéon Luzolo | Graphiste & Développeur Front-End",
  description:
    "Découvrez les outils, compétences techniques et créatives de Gédéon Luzolo, allant du branding, design graphique au développement front-end et UI/UX.",
  Keywords: [
    "Skills",
    "portfolio",
    "Gédéon Luzolo",
    "Photoshop",
    "Html",
    "Css",
    "Javascript",
    "Typescript",
    "Next js",
    "front-end",
    "React js",
    "Figma",
    "Adobe illustrator",
    "Tailwins css",
  ],
  image: {
    url: "/images/Opengraph/opengraph_skills.png",
    alt: "Photo des mes expériences profesionnels",
  },
});
export default function Page() {
  return (
    <div>
      <PageSkills />
    </div>
  );
}
