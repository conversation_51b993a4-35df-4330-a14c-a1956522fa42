"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import { Code2, Palette, Layout, ArrowRight, Sparkles } from "lucide-react";

const services = [
  {
    id: "web-development",
    name: "Développement Web",
    icon: Code2,
    description:
      "Applications web modernes et performantes avec les dernières technologies",
    color: "from-blue-500 to-cyan-500",
    iconBg: "bg-blue-500/20",
  },
  {
    id: "ui-ux-design",
    name: "UI/UX Design",
    icon: Layout,
    description:
      "Interfaces utilisateur intuitives et expériences utilisateur optimales",
    color: "from-purple-500 to-pink-500",
    iconBg: "bg-purple-500/20",
  },
  {
    id: "branding-design",
    name: "Design & Branding",
    icon: Palette,
    description: "Identité visuelle complète et stratégie de marque cohérente",
    color: "from-orange-500 to-red-500",
    iconBg: "bg-orange-500/20",
  },
];

export function Services() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section
      ref={ref}
      id="services"
      className="relative py-24 px-4 md:px-8 overflow-hidden"
    >
      {/* Background Elements */}
      {/* <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800" /> */}

      {/* Floating Orbs */}
      {/* <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-orange-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse delay-1000" /> */}

      <div className="relative max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={isInView ? { scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20 mb-6"
          >
            <Sparkles className="w-4 h-4 text-orange-500" />
            <span className="text-sm font-medium text-orange-600 dark:text-orange-400">
              Services Professionnels
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-slate-900 via-orange-900 to-orange-800 dark:from-white dark:via-orange-100 dark:to-orange-200 bg-clip-text text-transparent"
          >
            Mes Services
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed"
          >
            Des solutions digitales complètes pour transformer vos idées en
            réalité. De la conception à la réalisation, je vous accompagne dans
            tous vos projets.
          </motion.p>
        </motion.div>

        {/* Services Grid - 3 colonnes */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              animate={isInView ? { opacity: 1, y: 0, scale: 1 } : {}}
              transition={{
                duration: 0.8,
                delay: 0.5 + index * 0.1,
                type: "spring",
                bounce: 0.3,
              }}
              className="group relative"
            >
              {/* Card - Sans bordure dégradée */}
              <div className="relative h-full p-6 rounded-2xl bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border border-white/20 dark:border-slate-700/30 shadow-lg hover:shadow-xl transition-all duration-300">
                {/* Icon */}
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", bounce: 0.5 }}
                  className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${service.iconBg} mb-6`}
                >
                  <service.icon
                    className={`w-8 h-8 bg-gradient-to-r ${service.color} bg-clip-text text-transparent`}
                  />
                </motion.div>

                {/* Content */}
                <div className="space-y-4">
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white transition-all duration-300">
                    {service.name}
                  </h3>

                  <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                    {service.description}
                  </p>

                  {/* CTA Button */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`mt-6 inline-flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r ${service.color} text-white text-sm font-medium shadow-md hover:shadow-lg transition-all duration-300`}
                  >
                    <span>En savoir plus</span>
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
