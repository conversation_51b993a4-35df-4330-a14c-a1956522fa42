"use client";

import { motion } from "framer-motion";
import { Code, Palette, Layout } from "lucide-react";

const services = [
  {
    name: "Création de sites web ",
    icon: Code,
    description:
      "Conception de sites sur mesure adaptés à vos besoins, avec des solutions personnalisées et modernes.",
  },
  {
    name: "UI/UX Design",
    icon: Layout,
    description:
      "Création d'interfaces utilisateur intuitives et esthétiques pour offrir une expérience utilisateur optimale.",
  },

  {
    name: "Developpement Frontend",
    icon: Code,
    description:
      "Réalisation d’applications web interactives et réactives, adaptées à tous les types d'écrans.",
  },
  {
    name: "Design & Branding",
    icon: Palette,
    description:
      "Développement d’une identité visuelle complète et cohérente pour renforcer l'image de votre marque.",
  },
];

export function Services() {
  return (
    <section
      id="services"
      className="lg:py-28 py-16 px-4 md:px-8 backdrop-blur-md"
    >
      <motion.h2
        initial={{ opacity: 0, y: 80 }}
        whileInView={{ opacity: 1, y: 1 }}
        transition={{ duration: 1, delay: 0.3 }}
        className="text-3xl md:text-4xl font-bold mb-12 text-center"
      >
        Mes Services
      </motion.h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-[90%] lg:maw-w-6xl mx-auto">
        {services.map((service, index) => (
          <motion.div
            key={service.name}
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: index * 0.2 }}
            whileHover={{ scale: 1.1 }}
            // className=" bg-gradient-to-r from-orange-500 to-orange-700 text-zinc-50 rounded-lg p-6 shadow-lg transition-colors duration-100"
            className="bg-secondary dark:bg-blue-950/20 dark:border-none rounded-lg p-6 shadow-md dark:hover:shadow-primary/10  hover:border-primary/50 transition-colors duration-100"
          >
            <div className="flex items-center gap-5">
              <service.icon className="w-12 h-12 mb-4 text-primary" />
              <h3 className="text-xl mb-2 font-semibold ">{service.name}</h3>
            </div>
            <p className="text-justify px-3">{service.description}</p>
          </motion.div>
        ))}
      </div>
    </section>
  );
}
