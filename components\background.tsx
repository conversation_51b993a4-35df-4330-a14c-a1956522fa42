"use client";
import { motion } from "framer-motion";
import { ReactNode } from "react";

interface BackgroundProps {
  children: ReactNode;
}
const Background: React.FC<BackgroundProps> = ({ children }) => {
  return (
    <div>
      {/* Decorative elements */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.1 }}
        transition={{ duration: 1 }}
        className="fixed top-0 right-0 left-0 inset-0 grid grid-cols-2 -space-x-52 opacity-10 z-[-1]"
      >
        <div className="h-56 bg-gradient-to-br from-primary to-orange-400 blur-[200px]" />
        <div className="h-32 bg-gradient-to-r from-orange-700 to-orange-300 blur-[106px]" />
      </motion.div>
      {children}
    </div>
  );
};

export default Background;
