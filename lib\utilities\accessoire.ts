import {
  Briefcase,
  Code,
  GraduationCap,
  Home,
  Info,
  Wrench,
} from "lucide-react";
import { Experience } from "../types/globalTypes";

export const menuItems = [
  { name: "Accueil", href: "/", icon: Home },
  { name: "Qui suis-je ?", href: "/apropos", icon: Info },
  { name: "Portfolio", href: "/#portfolio", icon: Briefcase },
  { name: "Services", href: "/#services", icon: Wrench },
  { name: "Skills", href: "/skills", icon: Code },
  { name: "Expériences", href: "/experiences", icon: GraduationCap },
];

export const projects = [
  {
    id: 1,
    title: "Domicon ",
    category: "Website",
    description:
      "Plateforme moderne de recherche immobilière avec filtres avancés et interface intuitive pour faciliter la découverte de biens.",
    href: "https://domicon.vercel.app",
    target: "_blanc",
    image: "/images/Site/site4.png",
    stack: [
      "React",
      "Next.js",
      "TypeScript",
      "Tailwind CSS",
      "Prisma",
      "Nest js",
    ],
  },
  {
    id: 2,
    title: "LunaBetting",
    category: "Website",
    description:
      "Plateforme de paris sportifs en temps réel avec API robuste, gestion des utilisateurs, transactions sécurisées et interface responsive.",
    href: "https://preprod.lunabetting.com/",
    target: "_blanc",
    image: "/images/Site/site5.png",
    stack: ["Nest js", "PostgreSQL", "TypeORM", "Socket.io"],
  },

  {
    id: 3,
    title: "Fatech Academy ",
    category: "Website",
    description:
      "Site web éducatif moderne pour Fatech Academy avec interface moderne",
    href: "https://fatech-rdc.com",
    target: "_blanc",
    image: "/images/Site/site3.png",
    stack: ["Next.js", "TypeScript", "Tailwind CSS"],
  },
  {
    id: 4,
    title: "M6 TRAVEL",
    category: "Website",
    description:
      "Site web corporate professionnel pour M6 Travel avec présentation des services, galerie de destinations etc..",
    href: "https://m6travel.com",
    target: "_blanc",
    image: "/images/Site/site1.png",
    stack: ["HTML5", "CSS3", "JavaScript"],
  },

  {
    id: 5,
    title: "Portfolio",
    category: "Website",
    description:
      "Portfolio personnel interactif avec animations fluides, présentation de projets, galerie de créations et interface utilisateur moderne.",
    href: "https://portfolio-gedeon.vercel.app",
    target: "_blanc",
    image: "/images/Site/site2.png",
    stack: ["Next.js", "React", "TypeScript", "Tailwind CSS", "Framer Motion"],
  },
  {
    id: 6,
    title: "BLADE CORPORATION",
    category: "Branding",
    description:
      "Création complète de l'identité visuelle pour Blade Corporation : logo, charte graphique, supports de communication et déclinaisons digitales.",
    href: "https://www.behance.net/gallery/*********/Branding-blade-corporation",
    target: "_blanc",
    image: "/images/Design/branding1.jpg",
    stack: ["Photoshop", "Illustrator", "InDesign"],
  },
  {
    id: 7,
    title: "Toutes mes créas",
    category: "Design",
    description:
      "Collection complète de mes créations graphiques : affiches, illustrations, designs digitaux et projets artistiques personnels.",
    href: "/Gallery",
    image: "/images/tout.jpg",
    stack: ["Photoshop", "Illustrator", "InDesign"],
  },

  {
    id: 8,
    title: "CERCLE DIGITAL",
    category: "Branding",
    description:
      "Développement de l'identité de marque pour Cercle Digital : conception du logo, palette de couleurs, typographie et guidelines de marque.",
    href: "https://www.behance.net/gallery/215009461/BRANDING-CERCLE-DIGITAL",
    target: "_blanc",
    image: "/images/branding/branding1.jpg",
    stack: ["Illustrator", "Photoshop", "InDesign"],
  },
  {
    id: 9,
    title: "Design interface utilisateur REPAIR DRC",
    category: "UX",
    description:
      "Conception UX/UI complète pour l'application mobile REPAIR DRC : wireframes, prototypes interactifs",
    href: "https://www.figma.com/proto/qfQwdOa18iA7MAQygZq1Eu/Repro?node-id=4-984&t=iCTAQGqzAlxYBNoe-1",
    image: "/images/repairDRC.png",
    stack: ["Figma"],
  },
  {
    id: 10,
    title: "Collection logo",
    category: "Logo",
    target: "_blanc",
    description:
      "Portfolio de logos créés pour divers clients : startups tech, entreprises locales, associations et marques personnelles dans différents secteurs d'activité.",
    href: "https://www.behance.net/gallery/*********/LOGOFOLIO-2024",
    image: "/images/Logofolio/logofolio.jpg",
    stack: ["Illustrator", "Photoshop"],
  },
];

export const experiences: Experience[] = [
  {
    date: "Octobre 2020 - Aujoud'hui",
    company: "Graphiste designer freelance",
    description:
      "En tant que graphiste-designer indépendant, cette expérience implique pour moi la conception de visuels et d'identités visuelles pour divers clients. Cela inclut la création de logos, de supports marketing, d'illustrations et d'autres éléments graphiques, tout en répondant aux besoins spécifiques des clients dans divers secteurs.",
  },
  {
    date: "Fevrier 2022 - Aujoud'hui",
    company: "Developpeur Fullstack",
    description:
      "Mon expérience en developpement fullstack consiste à concevoir et à développer des interfaces utilisateur pour des sites web, en mettant l'accent sur l'esthétique et la fonctionnalité. Elle inclut l'utilisation des technologies modernes ainsi que la collaboration avec des clients pour concrétiser leurs projets en ligne.",
  },
  {
    date: "Janvier 2023 - Mai 2024",
    company: "NTECH GLOBAL’S",
    description:
      "Développement d'applications web interactives avec React,  Conception et implémentation d'APIs RESTful avec Express, Optimisation des performances frontend et backend etc...",
  },
  {
    date: "Juillet 2022 - Octobre 2023",
    company: "Contributeur de projet à la Regideso Kinshasa/Gombe",
    description:
      "Lors de ce projet, les responsabilités incluaient probablement l'assistance dans des projets liés à l'administration, la gestion informatique et le suivi du site pour la Regideso, l'entreprise de gestion de l'eau. Cela a permis de renforcer les compétences techniques dans un cadre professionnel structuré.",
  },
];

export const affichesImages = [
  "/images/Design/design3.jpg?height=400&width=600",
  "/images/Design/design1.jpg?height=300&width=300",
  "/images/Design/design2.jpg?height=300&width=300",
  "/images/Design/design8.jpg?height=300&width=300",
  "/images/Design/design4.jpg?height=300&width=300",
  "/images/Design/design5.jpg?height=300&width=300",
  "/images/Design/design7.jpg?height=300&width=300",
  "/images/Design/design10.jpg?height=300&width=300",
  "/images/Design/design9.jpg?height=300&width=300",
  "/images/Design/design18.jpg?height=300&width=300",
  "/images/Design/design11.jpg?height=300&width=300",
  "/images/Design/design17.jpg?height=300&width=300",
  "/images/Design/design12.jpg?height=300&width=300",
  "/images/Design/design14.jpg?height=300&width=300",
  "/images/Design/design15.jpg?height=400&width=600",
  "/images/Design/design16.jpg?height=400&width=600",
];

export const logosImages = [
  "/images/branding/branding1.jpg?height=300&width=300",
  "/images/Logofolio/logo7.jpg?height=300&width=300",
  "/images/Logofolio/logo5.jpg?height=300&width=300",
  "/images/Logofolio/logo1.jpg?height=300&width=300",
  "/images/Logofolio/logo2.jpg?height=300&width=300",
  "/images/Logofolio/logo3.jpg?height=300&width=300",
  "/images/Logofolio/logo4.jpg?height=300&width=300",
  "/images/Logofolio/logo6.jpg?height=300&width=300",
  "/images/Logofolio/logo8.jpg?height=300&width=300",
];

export const brandImages = [
  "/images/branding/branding1.jpg?height=300&width=300",
  "/images/branding/branding2.jpg?height=300&width=300",
  "/images/branding/branding3.jpg?height=300&width=300",
  "/images/branding/branding4.jpg?height=300&width=300",
  "/images/branding/branding5.jpg?height=300&width=300",
  "/images/branding/branding6.jpg?height=300&width=300",
  "/images/branding/branding7.jpg?height=300&width=300",
  "/images/branding/branding9.jpg?height=300&width=300",
  "/images/branding/branding8.jpg?height=300&width=300",
];

export const skills = [
  {
    id: "basic",
    title: "HTML5, CSS3, JAVASCRIPT",
    description:
      "Ces technologies fondamentales me permettent de structurer, styliser et rendre interactives les pages web.",
    icons: [
      "/images/Icones/html5.png",
      "/images/Icones/css3.png",
      "/images/Icones/javascript.png",
    ],
  },
  {
    id: "frontend",
    title: "Typescript, React Js, Next.js, TailwindCSS, Motion",
    description:
      "Ce stack moderne me permet de concevoir des applications web performantes, élégantes et réactives grâce à des composants modulaires et une gestion optimisée du front-end.",
    icons: [
      "/images/Icones/typescript.png",
      "/images/Icones/react.png",
      "/images/Icones/Nextjs.png",
      "/images/Icones/Tailwind.png",
      "/images/Icones/motion.png",
    ],
  },
  {
    id: "backend",
    title: "Nest js, Express Js, Prisma, Mongoose, Firebase",
    description:
      "Ce stack Backend moderne me permet de developper des Api, gerer les bases de données, et garantit une optimisation fiable des performances",
    icons: [
      "/images/Icones/logoNest.png",
      "/images/Icones/express.png",
      "/images/Icones/prisma.png",
      "/images/Icones/mongoose.png",
      "/images/Icones/firebase.png",
    ],
  },
  {
    id: "Design",
    title: "Mes outils de créas",
    description:
      "Ces logiciels sont essentiels me permettant de créer des designs visuels percutants",
    icons: [
      "/images/Icones/photoshop.png",
      "/images/Icones/illustrator.png",
      "/images/Icones/indesign.png",
      "/images/Icones/wonder.png",
    ],
  },
  {
    id: "ai",
    title: "Figma & Adobe Xd",
    description:
      "Des outils de prototypage et de conception d'interfaces utilisateurs.",
    icons: ["/images/Icones/figma.png", "/images/Icones/adobexd.png"],
  },
];

export const testimonials = [
  {
    quote:
      "Gédéon a su comprendre nos besoins dès le départ. Son design et son professionnalisme ont dépassé nos attentes.",
    name: "Jeremie DIAS",
    designation: "Directeur Marketing chez TechFlow",
    src: "/images/Avatars/avatar1.jpg",
  },
  {
    quote:
      "Nous recommandons vivement son travail. L'attention aux détails et la créativité de Gédéon sont remarquables.",
    name: "Carla BRUNI",
    designation: "CEO chez InnovateSphere",
    src: "/images/Avatars/avatar2.jpg",
  },
  {
    quote:
      "Toujours satisfaits du travail de ce jeune passionnant du numérique. Ses solutions dépassent nos attentes à chaque projet.",
    name: "Gabriel Neman",
    designation: "Responsable Digital chez CloudScale",
    src: "/images/Avatars/avatar3.jpg",
  },
];
