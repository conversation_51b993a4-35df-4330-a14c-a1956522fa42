import { Metadata } from "next";
import { seoConfig } from "./config";

type PropsMetadata = {
  title?: string;
  description?: string;
  image?: {
    url: string;
    alt: string;
  };
  Keywords?: string[];
};

export function generateMetadata({
  title,
  description,
  image,
  Keywords,
}: PropsMetadata): Metadata {
  const { default: defaultCongig } = seoConfig;
  const pagetitle = title || defaultCongig.title;
  const pageDesciption = description || defaultCongig.description;
  const pageImage = image || defaultCongig.images.og;
  const pageKeywords = Keywords || defaultCongig.keywords;

  return {
    title: pagetitle,
    description: pageDesciption,
    openGraph: {
      type: "website",
      title: pagetitle,
      images: pageImage,
      description: pageDesciption,
    },
    keywords: pageKeywords,
    authors: defaultCongig.authors,
    icons: defaultCongig.icons.icon,
  };
}
