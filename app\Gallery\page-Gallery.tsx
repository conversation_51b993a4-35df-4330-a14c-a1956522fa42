"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ImageModal } from "../../components/image-modal";
import Image from "next/image";
import BgPattern from "@/components/background/bg-pattern";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utilities/utils";
import { useLoading } from "@/context/loadingContext";
import {
  affichesImages,
  brandImages,
  logosImages,
} from "@/lib/utilities/accessoire";

interface ImageGridProps {
  images: string[];
  selectedImage: number | null;
  setSelectedImage: (index: number) => void;
}

const ImageGrid: React.FC<ImageGridProps> = ({ images, setSelectedImage }) => (
  <motion.div
    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 auto-rows-auto"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 1.3 }}
  >
    {images.map((image, index) => (
      <motion.div
        key={index}
        className={`relative cursor-pointer rounded-xl overflow-hidden ${
          index === 0 ? "md:col-span-2 md:row-span-2" : ""
        } ${index === 12 ? "md:col-span-2" : ""}`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: index * 0.5 }}
        whileHover={{ scale: 1.03 }}
        onClick={() => setSelectedImage(index)}
      >
        <div className="group aspect-square relative">
          <Image
            src={image}
            alt={`Gallery image ${index + 1}`}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
          />
          <motion.div
            className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            whileHover={{ opacity: 1 }}
          >
            <div className="absolute inset-0 flex items-center justify-center">
              <motion.span
                className="text-white text-lg font-medium"
                initial={{ opacity: 0, y: 10 }}
                whileHover={{ opacity: 1, y: 0 }}
              >
                Voir Image
              </motion.span>
            </div>
          </motion.div>
        </div>
      </motion.div>
    ))}
  </motion.div>
);

export default function PageGalley() {
  const [selectedImage, setSelectedImage] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState("Branding");

  const { setIsLoading } = useLoading();

  useEffect(() => {
    setIsLoading(false);
  }, [setIsLoading]);

  const currentImages =
    activeTab === "Branding"
      ? brandImages
      : activeTab === "logos"
      ? logosImages
      : affichesImages;

  return (
    <div className="p-8 pt-24 sm:pt-28">
      <BgPattern />

      <div className="w-[85%] m-auto relative">
        <div className="border-b-2 border-primary mb-8">
          <h1 className="text-4xl font-bold bggradient mb-6">Mes créas</h1>
          <p className="mb-6">
            Découvez mes récentes conceptions branding, logos et visuels pour
            divers clients.
          </p>
        </div>

        <Tabs
          defaultValue="Branding"
          className="mb-8"
          onValueChange={setActiveTab}
        >
          <TabsList>
            <TabsTrigger
              value="Branding"
              className={cn(
                "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",
                "transition-colors duration-200"
              )}
            >
              Branding
            </TabsTrigger>
            <TabsTrigger
              value="affiches"
              className={cn(
                "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",
                "transition-colors duration-200"
              )}
            >
              Affiches
            </TabsTrigger>
            <TabsTrigger
              value="logos"
              className={cn(
                "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",
                "transition-colors duration-200"
              )}
            >
              Logofolio
            </TabsTrigger>
          </TabsList>

          <TabsContent value="affiches">
            <AnimatePresence mode="wait">
              <ImageGrid
                images={affichesImages}
                selectedImage={selectedImage}
                setSelectedImage={(index: number) => setSelectedImage(index)}
              />
            </AnimatePresence>
          </TabsContent>
          <TabsContent value="logos">
            <AnimatePresence mode="wait">
              <ImageGrid
                images={logosImages}
                selectedImage={selectedImage}
                setSelectedImage={(index: number) => setSelectedImage(index)}
              />
            </AnimatePresence>
          </TabsContent>

          <TabsContent value="Branding">
            <AnimatePresence mode="wait">
              <ImageGrid
                images={brandImages}
                selectedImage={selectedImage}
                setSelectedImage={(index: number) => setSelectedImage(index)}
              />
            </AnimatePresence>

            <AnimatePresence mode="wait">
              <div className="static lg:absolute top-[72%] left-[45%]">
                <a href="https://www.behance.net/gedeonluzolo">
                  <Image
                    src="/images/Icones/behance.png"
                    alt="logo behance"
                    width={100}
                    height={100}
                    className="object-contain mx-auto mt-10 bg-primary p-2 rounded-full transition-transform duration-500 ease-in-out hover:scale-125"
                  />
                </a>
                <p className="text-center pt-7 flex items-center gap-5 justify-center">
                  Cliquer sur l&apos;icône pour continuer <br /> sur behance....
                </p>
              </div>
            </AnimatePresence>
          </TabsContent>
        </Tabs>
      </div>

      <ImageModal
        isOpen={selectedImage !== null}
        onClose={() => setSelectedImage(null)}
        images={currentImages}
        initialIndex={selectedImage || 0}
      />
    </div>
  );
}
