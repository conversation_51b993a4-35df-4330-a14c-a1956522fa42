"use client";
import { useEffect } from "react";
import { motion } from "framer-motion";

import BgPattern from "@/components/bg-pattern";
import Image from "next/image";
import { useLoading } from "@/context/loadingContext";

export default function PageApropos() {
  const { setIsLoading } = useLoading();
  useEffect(() => {
    setIsLoading(false);
  }, [setIsLoading]);

  return (
    <section className="relative min-h-screen w-full py-24 z-100 lg:py-28">
      <BgPattern />
      <div className="max-w-[90%] mx-auto px-4">
        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 2 }}
          className="mb-8 text-4xl font-bold"
        >
          Qui suis-je ?
        </motion.h1>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 3 }}
          className=""
        >
          <div className="">
            <div className="float-left">
              <Image
                src="/images/profil3.png"
                alt="Hero Image"
                width={400}
                height={400}
                className="object-contain"
              />
            </div>

            <div>
              <h1 className="font-bold text-2xl bggradient mb-6">
                Gedeon Luzolo,{" "}
              </h1>
              <p className="text-justify">
                Passionné par l’art, la technologie et l’innovation. Gedeon
                Luzolo possède un BAC+5 en conception de systèmes
                d&apos;informations informatique, Graphiste de formation,
                concepteur des sites web, je me spécialise dans la création
                d’identités visuelles percutantes et la conception d’expériences
                numériques fluides.
                <br />
                <br />
                J’utilise des outils et téchnologies modernes pour donner vie à
                mes idées et de concrétiser divers projects dans des
                environnements numériques interactifs. Ce qui me distingue,
                c’est ma capacité à m’adapter à divers projets et besoins. Que
                vous soyez une entreprise cherchant à créer une identité forte,
                une startup souhaitant lancer une application intuitive, ou un
                individu ayant besoin d’un contenu visuel impactant, je suis là
                pour collaborer avec vous et transformer votre vision en
                réalité.
              </p>

              <p className="mt-5 font-bold bggradient">
                Si vous êtes à la recherche d’un partenaire qui allie passion,
                expertise et écoute, je serais honoré de collaborer avec vous
                pour donner vie à vos projets.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
