"use client";

import { motion } from "framer-motion";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/card";
import { useLoading } from "@/context/loadingContext";
import { useEffect } from "react";
import { skills } from "@/lib/utilities/accessoire";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const cardVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut",
    },
  },
};

export default function PageSkills() {
  const { setIsLoading } = useLoading();

  useEffect(() => {
    setIsLoading(false);
  }, [setIsLoading]);

  return (
    <section className="min-h-screen py-20 sm:py-28 px-4 pb-16 sm:px-6 lg:px-8">
      <div className="max-w-[80%] mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1.2 }}
          className="text-center mb-5 sm:mb-12"
        >
          <h2 className="mt-4 text-2xl text-justify sm:text-center sm:text-3xl lg:text-4xl font-bold leading-tight">
            L&apos;ensemble des outils et téchnologies qui me permettent de
            créer de Sites, applications web, branding et des beaux visuels
          </h2>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12"
        >
          {skills.map((skill) => (
            <motion.div key={skill.id} variants={cardVariants}>
              <Card className="group relative overflow-hidden bg-gradient-to-r from-primary/10  backdrop-blur-sm transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center gap-4 mb-4">
                    {skill.icons.map((icon, index) => (
                      <motion.img
                        key={index}
                        src={icon}
                        alt=""
                        className="w-8 h-8 object-contain"
                        whileHover={{ scale: 2 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      />
                    ))}
                  </div>
                  <CardTitle className="text-xl mb-2">{skill.title}</CardTitle>
                  <CardDescription>{skill.description}</CardDescription>
                </CardHeader>
                <div className="absolute inset-0 border border-primary/20 rounded-lg" />
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 bg-gradient-to-r from-primary/30 to-transparent transition-opacity duration-300" />
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
