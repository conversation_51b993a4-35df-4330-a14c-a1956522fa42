import { <PERSON><PERSON> } from "@/components/ui/button";
import { motion, AnimatePresence, useInView } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FaFacebook, FaLinkedin, FaWhatsapp } from "react-icons/fa";
import { IoIosMail } from "react-icons/io";
import { Target } from "lucide-react";

export function ButtonContact() {
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 3 }}
      >
        <Dialog>
          <DialogTrigger asChild>
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
              className="text-center"
            >
              <div className="inline-flex items-center gap-4 px-6 py-2 rounded-2xl bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20 backdrop-blur-xl">
                <Target className="w-6 h-6 text-orange-500" />
                <span className="text-lg font-medium text-slate-700 dark:text-slate-300">
                  Prêt à démarrer votre projet ?
                </span>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-6 py-2 rounded-xl bg-gradient-to-r from-orange-500 to-orange-600 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Contactez-moi
                </motion.button>
              </div>
            </motion.div>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle> Parlons de votre projet</DialogTitle>
              <DialogDescription className="pt-2">
                Contactez moi par votre canal preféré
              </DialogDescription>
            </DialogHeader>
            <div className="flex justify-center space-x-6 mt-">
              <a
                href="http://wa.me/243854816076"
                className="text-gray-400 transition-colors hover:text-primary"
                target="_blanc"
              >
                <FaWhatsapp className="w-6 h-6" />
              </a>

              <a
                href="mailto:<EMAIL>"
                target="_blanc"
                className="text-gray-400 transition-colors hover:text-primary"
              >
                <IoIosMail className="w-8 h-8" />
              </a>
              <a
                href="https://www.linkedin.com/in/gedeon-luzolo?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"
                target="_blanc"
                className="text-gray-400 transition-colors hover:text-primary"
              >
                <FaLinkedin className="w-6 h-6" />
              </a>

              <a
                href="https://www.facebook.com/lzl.luzolo"
                target="_blanc"
                className="text-gray-400 transition-colors hover:text-primary"
              >
                <FaFacebook className="w-6 h-6" />
              </a>
            </div>
          </DialogContent>
        </Dialog>
      </motion.div>
    </AnimatePresence>
  );
}
