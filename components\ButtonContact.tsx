import { motion, AnimatePresence } from "framer-motion";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FaFacebook, FaLinkedin, FaWhatsapp } from "react-icons/fa";
import { IoIosMail } from "react-icons/io";
import { Target } from "lucide-react";

export function ButtonContact() {
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 3 }}
      >
        <Dialog>
          <DialogTrigger asChild>
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
              className="text-center"
            >
              {/* Version Desktop */}
              <div className="hidden sm:inline-flex items-center gap-3 px-4 py-2 rounded-xl bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20 backdrop-blur-xl">
                <Target className="w-5 h-5 text-orange-500" />
                <span className="text-base font-medium text-slate-700 dark:text-slate-300">
                  Prêt à démarrer votre projet ?
                </span>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-4 py-1.5 rounded-lg bg-gradient-to-r from-orange-500 to-orange-600 text-white text-sm font-medium shadow-md hover:shadow-lg transition-all duration-300"
                >
                  Contactez-moi
                </motion.button>
              </div>

              {/* Version Mobile */}
              <div className="sm:hidden flex flex-col gap-3 w-full max-w-lg">
                <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20 backdrop-blur-xl">
                  <Target className="w-4 h-4 text-orange-500" />
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    Démarrer votre projet
                  </span>
                </div>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full px-4 py-2.5 rounded-lg bg-gradient-to-r from-orange-500 to-orange-600 text-white text-sm font-medium shadow-md hover:shadow-lg transition-all duration-300"
                >
                  Contactez-moi
                </motion.button>
              </div>
            </motion.div>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle> Parlons de votre projet</DialogTitle>
              <DialogDescription className="pt-2">
                Contactez moi par votre canal preféré
              </DialogDescription>
            </DialogHeader>
            <div className="flex justify-center space-x-6 mt-">
              <a
                href="http://wa.me/243854816076"
                className="text-gray-400 transition-colors hover:text-primary"
                target="_blanc"
              >
                <FaWhatsapp className="w-6 h-6" />
              </a>

              <a
                href="mailto:<EMAIL>"
                target="_blanc"
                className="text-gray-400 transition-colors hover:text-primary"
              >
                <IoIosMail className="w-8 h-8" />
              </a>
              <a
                href="https://www.linkedin.com/in/gedeon-luzolo?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"
                target="_blanc"
                className="text-gray-400 transition-colors hover:text-primary"
              >
                <FaLinkedin className="w-6 h-6" />
              </a>

              <a
                href="https://www.facebook.com/lzl.luzolo"
                target="_blanc"
                className="text-gray-400 transition-colors hover:text-primary"
              >
                <FaFacebook className="w-6 h-6" />
              </a>
            </div>
          </DialogContent>
        </Dialog>
      </motion.div>
    </AnimatePresence>
  );
}
