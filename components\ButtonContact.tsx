import { But<PERSON> } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FaFacebook, FaLinkedin, FaWhatsapp } from "react-icons/fa";
import { IoIosMail } from "react-icons/io";
import { MdCall } from "react-icons/md";

export function ButtonContact() {
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 3 }}
      >
        <Dialog>
          <DialogTrigger asChild>
            <Button className="bg-primary hover:bg-primary/80">
              Parlons de votre projet
              <MdCall />
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle> Parlons de votre projet</DialogTitle>
              <DialogDescription className="pt-2">
                Contactez moi par votre canal preféré
              </DialogDescription>
            </DialogHeader>
            <div className="flex justify-center space-x-6 mt-">
              <a
                href="http://wa.me/243854816076"
                className="text-gray-400 transition-colors hover:text-primary"
                target="_blanc"
              >
                <FaWhatsapp className="w-6 h-6" />
              </a>

              <a
                href="mailto:<EMAIL>"
                target="_blanc"
                className="text-gray-400 transition-colors hover:text-primary"
              >
                <IoIosMail className="w-8 h-8" />
              </a>
              <a
                href="https://www.linkedin.com/in/gedeon-luzolo?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"
                target="_blanc"
                className="text-gray-400 transition-colors hover:text-primary"
              >
                <FaLinkedin className="w-6 h-6" />
              </a>

              <a
                href="https://www.facebook.com/lzl.luzolo"
                target="_blanc"
                className="text-gray-400 transition-colors hover:text-primary"
              >
                <FaFacebook className="w-6 h-6" />
              </a>
            </div>
          </DialogContent>
        </Dialog>
      </motion.div>
    </AnimatePresence>
  );
}
