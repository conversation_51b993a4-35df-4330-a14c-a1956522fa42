"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

import { FaEye, FaCode, FaPalette } from "react-icons/fa";
import { projects } from "@/lib/utilities/accessoire";
import { cn } from "@/lib/utilities/utils";
import { ProjectCard } from "./ProjectCard";

const categories = ["Tout", "Website", "Branding", "UX", "Design", "Logo"];

export function AdvancedPortfolio() {
  const [activeCategory, setActiveCategory] = useState("Tout");
  const [hoveredProject, setHoveredProject] = useState<number | null>(null);

  const filteredProjects =
    activeCategory === "Tout"
      ? projects
      : projects.filter((project) => project.category === activeCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Website":
        return <FaCode className="w-4 h-4" />;
      case "Design":
        return <FaPalette className="w-4 h-4" />;
      case "Branding":
        return <FaPalette className="w-4 h-4" />;
      case "Logo":
        return <FaPalette className="w-4 h-4" />;
      default:
        return <FaEye className="w-4 h-4" />;
    }
  };

  return (
    <section className="relative min-h-screen py-20">
      <div className="relative z-20 max-w-7xl mx-auto px-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-6xl md:text-7xl font-bold mb-6 dark:text-slate-300 ">
            Portfolio
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
            Découvrez mes créations où l&apos;innovation rencontre
            l&apos;excellence
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
        >
          {categories.map((category, index) => (
            <motion.button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={cn(
                "relative px-8 py-4 rounded-2xl font-medium transition-all duration-300",
                "backdrop-blur-sm border border-white/20 dark:border-white/10",
                activeCategory === category
                  ? "bg-gradient-to-r from-orange-500/20 via-yellow-500/20 to-orange-600/20 text-orange-700 dark:text-orange-300 shadow-lg"
                  : "bg-white/10 dark:bg-white/5 text-slate-700 dark:text-slate-300 hover:bg-white/20 dark:hover:bg-white/10"
              )}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              viewport={{ once: true }}
            >
              <span className="flex items-center gap-2">
                {getCategoryIcon(category)}
                {category}
              </span>
              {activeCategory === category && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-orange-500/20 via-yellow-500/20 to-orange-600/20 rounded-2xl -z-10"
                  layoutId="activeCategory"
                  transition={{ type: "spring", bounce: 0.15, duration: 0.4 }}
                />
              )}
            </motion.button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          layout
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 items-start"
        >
          <AnimatePresence>
            {filteredProjects.map((project, index) => (
              <ProjectCard
                key={`${activeCategory}-${project.id}`}
                project={project}
                index={index}
                isHovered={hoveredProject === project.id}
                hasHoveredSibling={
                  hoveredProject !== null && hoveredProject !== project.id
                }
                onHover={() => setHoveredProject(project.id)}
                onLeave={() => setHoveredProject(null)}
              />
            ))}
          </AnimatePresence>
        </motion.div>
      </div>
    </section>
  );
}
