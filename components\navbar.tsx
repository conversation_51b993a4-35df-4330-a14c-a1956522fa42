"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Moon, Sun, Menu, X } from "lucide-react";
import { useTheme } from "next-themes";
import Link from "next/link";
import { menuItems } from "@/lib/accessoire";

export function Navbar() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => setMounted(true), []);

  if (!mounted) return null;

  return (
    <>
      <motion.nav
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="fixed top-6 left-0 right-0 z-50"
      >
        <div className="max-w-7xl mx-auto px-6">
          {/* Container avec glassmorphism moderne */}
          <div className="relative backdrop-blur-md bg-white/20 dark:bg-white/5 border border-white/20 dark:border-white/10 rounded-2xl shadow-xl shadow-black/5 dark:shadow-black/20">
            {/* Gradient border animé */}
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-orange-500/20 via-yellow-500/20 to-orange-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm -z-10" />

            <div className="relative px-6 lg:px-8">
              <div className="flex items-center justify-between h-16">
                {/* Logo */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="flex-shrink-0"
                >
                  <Link href="./">
                    <motion.span
                      className="text-2xl font-bold bg-gradient-to-r from-orange-500 via-yellow-500 to-orange-600 dark:from-orange-400 dark:via-yellow-400 dark:to-orange-500 bg-clip-text text-transparent"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.2 }}
                    >
                      Gede
                    </motion.span>
                  </Link>
                </motion.div>

                {/* Menu Desktop */}
                <div className="hidden md:flex items-center space-x-1">
                  {menuItems.map((item, index) => (
                    <motion.div
                      key={item.name}
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.1 * index + 0.3 }}
                    >
                      <Link href={item.href}>
                        <motion.div
                          className="relative px-6 py-2 rounded-xl font-medium text-slate-700 dark:text-slate-300 transition-all duration-300 hover:text-orange-600 dark:hover:text-orange-400"
                          whileHover={{
                            scale: 1.05,
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                          }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <span className="relative z-10">{item.name}</span>

                          {/* Effet de hover avec glassmorphism */}
                          <motion.div
                            className="absolute inset-0 rounded-xl bg-gradient-to-r from-orange-500/10 to-yellow-500/10 opacity-0"
                            whileHover={{ opacity: 1 }}
                            transition={{ duration: 0.3 }}
                          />
                        </motion.div>
                      </Link>
                    </motion.div>
                  ))}
                </div>

                {/* Theme Toggle Button */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="flex items-center"
                >
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() =>
                      setTheme(theme === "dark" ? "light" : "dark")
                    }
                    className="relative p-3 rounded-xl backdrop-blur-sm bg-transparent border border-white/20 dark:border-white/10 text-slate-700 dark:text-slate-300 transition-all duration-300 hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-yellow-500/10 hover:text-orange-600 dark:hover:text-orange-400 hover:shadow-lg hover:shadow-orange-500/25 mr-2"
                  >
                    <motion.div
                      initial={false}
                      animate={{ rotate: theme === "dark" ? 180 : 0 }}
                      transition={{ duration: 0.5, ease: "easeInOut" }}
                    >
                      {theme === "dark" ? (
                        <Sun className="w-5 h-5" />
                      ) : (
                        <Moon className="w-5 h-5" />
                      )}
                    </motion.div>
                  </motion.button>

                  {/* Menu Mobile Button */}
                  <div className="md:hidden">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setIsOpen(!isOpen)}
                      className={`relative p-3 rounded-xl backdrop-blur-sm border border-white/20 dark:border-white/10 transition-all duration-300 ${
                        isOpen
                          ? "bg-gradient-to-r from-orange-500/20 to-yellow-500/20 text-orange-600 dark:text-orange-400 shadow-lg shadow-orange-500/25"
                          : "bg-transparent text-slate-700 dark:text-slate-300 hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-yellow-500/10 hover:text-orange-600 dark:hover:text-orange-400 hover:shadow-lg hover:shadow-orange-500/25"
                      }`}
                    >
                      <motion.div
                        animate={{ rotate: isOpen ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        {isOpen ? (
                          <X className="h-5 w-5" />
                        ) : (
                          <Menu className="h-5 w-5" />
                        )}
                      </motion.div>
                    </motion.button>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </motion.nav>

      {/* Mobile Navbar - Inchangé */}
      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4 }}
              className="fixed inset-0 backdrop-blur-sm z-40 md:hidden"
              onClick={() => setIsOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95, x: "100%" }}
              animate={{ opacity: 1, scale: 1, x: 0 }}
              exit={{ opacity: 0, scale: 0.95, x: "100%" }}
              transition={{ duration: 0.4 }}
              className="fixed top-28 right-4 w-[90%] mx-auto rounded-3xl h-auto pb-5 bg-white/50 dark:bg-secondary/50 backdrop-blur-lg shadow-2xl z-50 md:hidden"
            >
              <nav className="flex flex-col space-y-2 mt-5 px-4">
                {menuItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <motion.div
                      key={item.name}
                      whileHover={{ scale: 1.02, x: 5 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Link
                        href={item.href}
                        className="flex items-center space-x-6 px-4 py-4 rounded-2xl hover:bg-primary/50 transition-all duration-200"
                        onClick={() => setIsOpen(false)}
                      >
                        <Icon className="w-5 h-5" />
                        <span className="text-md font-medium">{item.name}</span>
                      </Link>
                    </motion.div>
                  );
                })}
              </nav>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}
