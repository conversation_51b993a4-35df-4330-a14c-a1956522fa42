import PageGalley from "./page-Gallery";
import { generateMetadata } from "@/lib/seo/generateMetadata";

export const metadata = generateMetadata({
  title:
    "Galerie de créas de Gédéon Luzolo | Graphiste & Développeur Front-End",
  description:
    "Explorez la galerie de Géd<PERSON>on <PERSON>, présentant ses meilleures créations graphiques, branding et logotype",
  Keywords: [
    "Galerie",
    "portfolio",
    "Gédéon Luzolo",
    "Branding",
    "Logotype",
    "design graphique",
    " UI/UX",
    "projets web",
    "front-end",
  ],
  image: {
    url: "/images/Opengraph/opengraph_gallery.png",
    alt: "Aperçu des mes créas",
  },
});
export default function Page() {
  return (
    <div>
      <PageGalley />
    </div>
  );
}
